<template>
    <div class="email-onboarding">
        <!-- Header -->
        <div class="mb-6">
            <h2 class="text-3xl font-bold text-gray-900 mb-2">Email Integration Setup</h2>
            <p class="text-gray-600">Set up email forwarding for your chatbot to handle email inquiries automatically.</p>
        </div>

        <!-- Progress Steps -->
        <div class="mb-8">
            <Steps :model="stepItems" :readonly="false" :activeStep="currentStep - 1" class="mb-6">
                <template #item="{ item, index, active, highlighted }">
                    <div class="flex flex-col items-center">
                        <div
                            :class="[
                                'flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all duration-200',
                                active || highlighted ? 'bg-primary-500 border-primary-500 text-white' :
                                item.completed ? 'bg-green-500 border-green-500 text-white' :
                                'bg-gray-100 border-gray-300 text-gray-500'
                            ]"
                        >
                            <i :class="item.icon" class="text-lg"></i>
                        </div>
                        <span
                            :class="[
                                'mt-2 text-sm font-medium text-center',
                                active || highlighted ? 'text-primary-600' :
                                item.completed ? 'text-green-600' :
                                'text-gray-500'
                            ]"
                        >
                            {{ item.title }}
                        </span>
                    </div>
                </template>
            </Steps>
        </div>

        <!-- Step Content -->
        <Card class="mb-6">
            <template #content>
                <!-- Step 1: Email Input -->
                <div v-if="currentStep === 1" class="space-y-6">
                    <div class="text-center mb-6">
                        <i class="pi pi-envelope text-4xl text-primary-500 mb-4"></i>
                        <h3 class="text-xl font-semibold mb-2">Enter Your Email Address</h3>
                        <p class="text-gray-600">This will be your default email for chatbot responses and notifications.</p>
                    </div>

                    <div class="max-w-md mx-auto">
                        <div class="field">
                            <label for="email" class="block font-medium mb-2">Email Address</label>
                            <IconField>
                                <InputIcon class="pi pi-envelope" />
                                <InputText
                                    id="email"
                                    v-model="emailForm.email"
                                    placeholder="Enter your email address"
                                    :class="{ 'p-invalid': emailForm.error }"
                                    @blur="validateEmailInput"
                                    @keyup.enter="handleStep1Continue"
                                    fluid
                                />
                            </IconField>
                            <small v-if="emailForm.error" class="p-error">{{ emailForm.error }}</small>
                        </div>

                        <div class="flex justify-center mt-6">
                            <Button
                                label="Continue"
                                icon="pi pi-arrow-right"
                                iconPos="right"
                                @click="handleStep1Continue"
                                :disabled="!emailForm.isValid || isLoading"
                                :loading="isLoading"
                                class="px-8"
                            />
                        </div>
                    </div>
                </div>

                <!-- Step 2: Email Forwarding Instructions -->
                <div v-if="currentStep === 2" class="space-y-6">
                    <div class="text-center mb-6">
                        <i class="pi pi-forward text-4xl text-primary-500 mb-4"></i>
                        <h3 class="text-xl font-semibold mb-2">Set Up Email Forwarding</h3>
                        <p class="text-gray-600">Forward your emails to our system so the chatbot can respond automatically.</p>
                    </div>

                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center mb-2">
                            <i class="pi pi-info-circle text-blue-600 mr-2"></i>
                            <span class="font-medium text-blue-800">Forward emails to:</span>
                        </div>
                        <div class="bg-white border rounded px-3 py-2 font-mono text-sm">
                            {{ forwardingEmail }}
                        </div>
                        <Button
                            icon="pi pi-copy"
                            text
                            size="small"
                            @click="copyForwardingEmail"
                            class="mt-2"
                            label="Copy Address"
                        />
                    </div>

                    <!-- Email Provider Instructions -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <Card v-for="provider in emailProviders" :key="provider.name" class="cursor-pointer hover:shadow-md transition-shadow">
                            <template #content>
                                <div @click="selectedProvider = selectedProvider === provider ? null : provider">
                                    <div class="flex items-center justify-between mb-3">
                                        <div class="flex items-center">
                                            <i :class="provider.icon" class="text-2xl mr-3"></i>
                                            <span class="font-medium">{{ provider.name }}</span>
                                        </div>
                                        <i :class="selectedProvider === provider ? 'pi pi-chevron-up' : 'pi pi-chevron-down'"></i>
                                    </div>

                                    <Transition name="slide-down">
                                        <div v-if="selectedProvider === provider" class="space-y-2">
                                            <ol class="list-decimal list-inside space-y-1 text-sm text-gray-700">
                                                <li v-for="(instruction, index) in provider.instructions" :key="index">
                                                    {{ instruction }}
                                                </li>
                                            </ol>
                                            <a
                                                v-if="provider.helpUrl"
                                                :href="provider.helpUrl"
                                                target="_blank"
                                                class="inline-flex items-center text-primary-600 hover:text-primary-700 text-sm mt-2"
                                            >
                                                <i class="pi pi-external-link mr-1"></i>
                                                Official Help Guide
                                            </a>
                                        </div>
                                    </Transition>
                                </div>
                            </template>
                        </Card>
                    </div>

                    <div class="flex justify-center mt-8">
                        <Button
                            label="I've Set Up Email Forwarding"
                            icon="pi pi-check"
                            @click="handleStep2Continue"
                            class="px-8"
                            severity="success"
                        />
                    </div>
                </div>

                <!-- Step 3: Email Validation -->
                <div v-if="currentStep === 3" class="space-y-6">
                    <div class="text-center mb-6">
                        <i class="pi pi-shield text-4xl text-primary-500 mb-4"></i>
                        <h3 class="text-xl font-semibold mb-2">Validate Email Integration</h3>
                        <p class="text-gray-600">We'll send a test email to verify your forwarding setup is working correctly.</p>
                    </div>

                    <div v-if="!isWaitingForValidation" class="text-center">
                        <Button
                            label="Start Validation"
                            icon="pi pi-play"
                            @click="startEmailValidation"
                            :loading="isValidating"
                            class="px-8"
                        />
                    </div>

                    <div v-else class="text-center space-y-4">
                        <ProgressSpinner />
                        <div>
                            <h4 class="font-medium mb-2">Waiting for email validation...</h4>
                            <p class="text-gray-600 text-sm">
                                We've sent a test email to <strong>{{ emailForm.email }}</strong>.
                                Please check that it's forwarded to our system.
                            </p>
                            <p class="text-gray-500 text-xs mt-2">
                                This may take a few minutes. The page will automatically update when validation is complete.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Step 4: Email Template -->
                <div v-if="currentStep === 4" class="space-y-6">
                    <div class="text-center mb-6">
                        <i class="pi pi-file-edit text-4xl text-primary-500 mb-4"></i>
                        <h3 class="text-xl font-semibold mb-2">Configure Email Template</h3>
                        <p class="text-gray-600">Customize the email template that will be used when the chatbot responds to inquiries.</p>
                    </div>

                    <div class="space-y-4">
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <div class="flex items-start">
                                <i class="pi pi-exclamation-triangle text-yellow-600 mr-2 mt-1"></i>
                                <div>
                                    <p class="text-yellow-800 text-sm">
                                        <strong>Important:</strong> Your template must include <code class="bg-yellow-100 px-1 rounded">{{content}}</code>
                                        where you want the chatbot's response to appear.
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="field">
                            <label for="template" class="block font-medium mb-2">Email Template</label>
                            <Editor
                                v-model="template"
                                editorStyle="height: 250px"
                                placeholder="Your email template here..."
                                @text-change="validateTemplate"
                            />
                            <div v-if="templateValidation.errors.length" class="mt-2">
                                <small v-for="error in templateValidation.errors" :key="error" class="p-error block">
                                    {{ error }}
                                </small>
                            </div>
                        </div>

                        <div class="flex justify-center space-x-4">
                            <Button
                                label="Use Default Template"
                                icon="pi pi-refresh"
                                severity="secondary"
                                @click="useDefaultTemplate"
                                outlined
                            />
                            <Button
                                label="Save Template"
                                icon="pi pi-check"
                                @click="handleStep4Continue"
                                :disabled="!templateValidation.isValid || isLoading"
                                :loading="isLoading"
                                class="px-8"
                            />
                        </div>
                    </div>
                </div>

                <!-- Step 5: Complete -->
                <div v-if="currentStep === 5" class="text-center space-y-6">
                    <div class="mb-6">
                        <i class="pi pi-check-circle text-6xl text-green-500 mb-4"></i>
                        <h3 class="text-2xl font-semibold mb-2 text-green-700">Setup Complete!</h3>
                        <p class="text-gray-600">Your email integration is now active and ready to handle inquiries.</p>
                    </div>

                    <div class="bg-green-50 border border-green-200 rounded-lg p-6 max-w-md mx-auto">
                        <h4 class="font-medium text-green-800 mb-3">What happens next?</h4>
                        <ul class="text-sm text-green-700 space-y-2 text-left">
                            <li class="flex items-start">
                                <i class="pi pi-check text-green-600 mr-2 mt-1 text-xs"></i>
                                Emails sent to your address will be forwarded to our system
                            </li>
                            <li class="flex items-start">
                                <i class="pi pi-check text-green-600 mr-2 mt-1 text-xs"></i>
                                Our AI chatbot will analyze and respond automatically
                            </li>
                            <li class="flex items-start">
                                <i class="pi pi-check text-green-600 mr-2 mt-1 text-xs"></i>
                                Responses will be sent using your custom template
                            </li>
                        </ul>
                    </div>

                    <Button
                        label="Done"
                        icon="pi pi-home"
                        @click="$emit('complete')"
                        class="px-8"
                        size="large"
                    />
                </div>
            </template>
        </Card>
    </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useToast } from 'primevue/usetoast';
import { useAuthStore } from '@/entities/auth';
import { useTenantStore } from '@/entities/tenant/store';
import { onSnapshot, doc } from 'firebase/firestore';
import { firestore } from '@/firebase';
import { EmailOnboardingService } from './services/emailOnboardingService';
import { EMAIL_PROVIDERS, DEFAULT_EMAIL_TEMPLATE } from './types';
import type { EmailValidationForm, TemplateValidation, EmailProvider, OnboardingStep } from './types';

// Components
import Steps from 'primevue/steps';
import Card from 'primevue/card';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';
import ProgressSpinner from 'primevue/progressspinner';
import Editor from 'primevue/editor';

// Emits
const emit = defineEmits<{
    complete: [];
}>();

// Stores
const toast = useToast();
const authStore = useAuthStore();
const tenantStore = useTenantStore();

// Services
const emailService = new EmailOnboardingService();

// State
const currentStep = ref(1);
const isLoading = ref(false);
const isValidating = ref(false);
const isWaitingForValidation = ref(false);
const selectedProvider = ref<EmailProvider | null>(null);
const template = ref(DEFAULT_EMAIL_TEMPLATE);
const tenantUnsubscribe = ref<(() => void) | null>(null);

// Email form
const emailForm = ref<EmailValidationForm>({
    email: '',
    isValid: false,
    error: undefined
});

// Template validation
const templateValidation = ref<TemplateValidation>({
    isValid: true,
    hasContentPlaceholder: true,
    errors: []
});

// Computed
const stepItems = computed<OnboardingStep[]>(() => [
    {
        id: 1,
        title: 'Email Address',
        description: 'Enter your email',
        icon: 'pi pi-envelope',
        completed: currentStep.value > 1
    },
    {
        id: 2,
        title: 'Email Forwarding',
        description: 'Set up forwarding',
        icon: 'pi pi-forward',
        completed: currentStep.value > 2
    },
    {
        id: 3,
        title: 'Validation',
        description: 'Verify setup',
        icon: 'pi pi-shield',
        completed: currentStep.value > 3
    },
    {
        id: 4,
        title: 'Template',
        description: 'Configure template',
        icon: 'pi pi-file-edit',
        completed: currentStep.value > 4
    }
]);

const emailProviders = computed(() => EMAIL_PROVIDERS);

const forwardingEmail = computed(() => {
    const userData = authStore.userData;
    if (userData?.tenantId) {
        return EmailOnboardingService.getForwardingEmail(userData.tenantId);
    }
    return '';
});

// Methods
const validateEmailInput = () => {
    const validation = EmailOnboardingService.validateEmailForm(emailForm.value.email);
    emailForm.value = validation;
};

const handleStep1Continue = async () => {
    validateEmailInput();
    if (!emailForm.value.isValid) return;

    try {
        isLoading.value = true;
        const userData = authStore.userData;
        if (!userData?.tenantId) {
            throw new Error('User tenant not found');
        }

        await emailService.saveEmailAddress(userData.tenantId, emailForm.value.email);
        currentStep.value = 2;

        toast.add({
            severity: 'success',
            summary: 'Email Saved',
            detail: 'Your email address has been saved successfully.',
            life: 3000
        });
    } catch (error) {
        console.error('Error saving email:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to save email address. Please try again.',
            life: 5000
        });
    } finally {
        isLoading.value = false;
    }
};

const handleStep2Continue = () => {
    currentStep.value = 3;
};

const copyForwardingEmail = async () => {
    try {
        await navigator.clipboard.writeText(forwardingEmail.value);
        toast.add({
            severity: 'success',
            summary: 'Copied',
            detail: 'Forwarding email address copied to clipboard.',
            life: 2000
        });
    } catch (error) {
        console.error('Failed to copy:', error);
        toast.add({
            severity: 'warn',
            summary: 'Copy Failed',
            detail: 'Please manually copy the email address.',
            life: 3000
        });
    }
};

const startEmailValidation = async () => {
    try {
        isValidating.value = true;
        await emailService.validateEmail(emailForm.value.email);
        isWaitingForValidation.value = true;

        toast.add({
            severity: 'info',
            summary: 'Validation Started',
            detail: 'Test email sent. Please check your email forwarding.',
            life: 5000
        });
    } catch (error) {
        console.error('Error starting validation:', error);
        toast.add({
            severity: 'error',
            summary: 'Validation Error',
            detail: 'Failed to start email validation. Please try again.',
            life: 5000
        });
    } finally {
        isValidating.value = false;
    }
};

const validateTemplate = () => {
    templateValidation.value = EmailOnboardingService.validateEmailTemplate(template.value);
};

const useDefaultTemplate = () => {
    template.value = DEFAULT_EMAIL_TEMPLATE;
    validateTemplate();
};

const handleStep4Continue = async () => {
    validateTemplate();
    if (!templateValidation.value.isValid) return;

    try {
        isLoading.value = true;
        const userData = authStore.userData;
        if (!userData?.tenantId) {
            throw new Error('User tenant not found');
        }

        await emailService.saveEmailTemplate(userData.tenantId, template.value);
        currentStep.value = 5;

        toast.add({
            severity: 'success',
            summary: 'Template Saved',
            detail: 'Your email template has been saved successfully.',
            life: 3000
        });
    } catch (error) {
        console.error('Error saving template:', error);
        toast.add({
            severity: 'error',
            summary: 'Error',
            detail: 'Failed to save email template. Please try again.',
            life: 5000
        });
    } finally {
        isLoading.value = false;
    }
};

// Initialize component
const initializeComponent = async () => {
    try {
        const userData = await authStore.getUserData();
        if (!userData?.tenantId) {
            throw new Error('User tenant not found');
        }

        const tenant = await tenantStore.getTenant(userData.tenantId);
        if (!tenant) {
            throw new Error('Tenant not found');
        }

        // Set up real-time listener for tenant changes
        const tenantDocRef = doc(firestore, 'companies', userData.tenantId);
        tenantUnsubscribe.value = onSnapshot(tenantDocRef, (doc) => {
            if (doc.exists()) {
                const tenantData = { id: doc.id, ...doc.data() };

                // Check if email validation status changed
                if (tenantData.email?.validated && isWaitingForValidation.value) {
                    isWaitingForValidation.value = false;
                    currentStep.value = 4;

                    toast.add({
                        severity: 'success',
                        summary: 'Email Validated',
                        detail: 'Your email forwarding has been successfully validated!',
                        life: 5000
                    });
                }
            }
        });

        // Initialize form with existing data
        if (tenant.email?.defaultEmail) {
            emailForm.value.email = tenant.email.defaultEmail;
            emailForm.value.isValid = true;
        }

        if (tenant.email?.template) {
            template.value = tenant.email.template;
        }

        // Set current step based on existing data
        currentStep.value = EmailOnboardingService.getCurrentStep(tenant);

        // If already waiting for validation, set the flag
        if (tenant.email?.defaultEmail && !tenant.email?.validated && currentStep.value === 3) {
            isWaitingForValidation.value = true;
        }

    } catch (error) {
        console.error('Error initializing component:', error);
        toast.add({
            severity: 'error',
            summary: 'Initialization Error',
            detail: 'Failed to load email onboarding data.',
            life: 5000
        });
    }
};

// Lifecycle
onMounted(() => {
    initializeComponent();
    validateTemplate();
});

onUnmounted(() => {
    if (tenantUnsubscribe.value) {
        tenantUnsubscribe.value();
    }
});

// Watch for template changes
watch(template, () => {
    validateTemplate();
});
</script>

<style scoped>
.email-onboarding {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.slide-down-enter-active,
.slide-down-leave-active {
    transition: all 0.3s ease;
    overflow: hidden;
}

.slide-down-enter-from,
.slide-down-leave-to {
    max-height: 0;
    opacity: 0;
}

.slide-down-enter-to,
.slide-down-leave-from {
    max-height: 500px;
    opacity: 1;
}

.field {
    margin-bottom: 1rem;
}

.p-steps .p-steps-item .p-steps-title {
    font-size: 0.875rem;
    font-weight: 500;
}

.transition-all {
    transition-property: all;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    transition-duration: 200ms;
}
</style>
