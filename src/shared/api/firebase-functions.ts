import { functions } from '@/firebase';
import { httpsCallable } from 'firebase/functions';
import { User as UserApp, UserFilters } from '@/entities/user';
import { User } from 'firebase/auth';
import { Appointment } from '@/entities/appointments';
import { followUpParams } from '@/entities/lead';

export const requestSaveUserFirebase = httpsCallable<UserApp, User>(functions, 'onSaveUser');

export type UserDeleteParam = {
    userId: string;
};
export const requestDeleteUserFirebase = httpsCallable<UserDeleteParam, boolean>(functions, 'onDeleteUser');

export type UserDeleteParams = {
    userIds: string[];
};
export const requestDeleteUsersFirebase = httpsCallable<UserDeleteParams, boolean>(functions, 'onDeleteUsers');

export type FetchUsersParams = {
    filters: UserFilters;
    pageSize: number;
    page: number;
};
export const requestFetchUsersFirebase = httpsCallable<FetchUsersParams, { resulta: UserApp[]; totalSiza: number }>(functions, 'onUsersFetchRecords');

export type LeadIdsParams = {
    ids?: string[];
    id?: string[];
};
export const requestLeadAnalysisByIdsFirebase = httpsCallable<LeadIdsParams, boolean>(functions, 'onAILeadAnalysisByIds');

export type FollowUpParams = {
    ids?: string[];
    id?: string[];
    tenantId?: string;
    messageParams?: followUpParams;
};
export const requestGetFollowUpMessageFirebase = httpsCallable<FollowUpParams, boolean | string>(functions, 'onAIGetFollowUpMessage');
export const requestSendFollowUpMessageFirebase = httpsCallable<FollowUpParams, boolean>(functions, 'onAISendFollowUp');
export const requestSendFollowUpMessageByIdsFirebase = httpsCallable<FollowUpParams, boolean>(functions, 'onAIFollowUpByIds');

export type AppointmentParamsFetchAvailableSlots = {
    preferredAppointmentDate?: number;
    selectedLocation?: string;
    tenantId?: string;
    selectedLocationLatLong?: string;
};
export const requestFetchAvailableSlotsFirebase = httpsCallable<AppointmentParamsFetchAvailableSlots, any>(functions, 'checkAvailableSlots');

export const requestAddAppointmentToCalendarFirebase = httpsCallable<Appointment, any>(functions, 'addAppointmentEvent');

export const requestCreateTenantFirebase = httpsCallable<any, any>(functions, 'onRegisterTenant');
