import * as admin from 'firebase-admin';
import * as logger from 'firebase-functions/lib/logger';

export async function validateFeatures(feature: string, validateValue: any) {
    let tenantSnapshot: any;

    const tenantCollection = admin.firestore().collection('companies');
    switch (feature) {
        case 'whatsapp':
            tenantSnapshot = await tenantCollection.where('email.emails', 'array-contains-any', [to]).get();
            leadDocRef = leadResult.leadDocRef;
            leadData = leadResult.leadData;
            break;
        case 'facebook':
            leadResult = await handelLeadDocumentFacebook(req);
            leadDocRef = leadResult.leadDocRef;
            leadData = leadResult.leadData;
            break;
        case 'email':
            leadResult = await handelLeadDocumentEmail(req);
            leadDocRef = leadResult.leadDocRef;
            leadData = leadResult.leadData;
    }

    if (tenantSnapshot.docs.length > 0) {
        logger.info(`Email: Found tenant ${tenantSnapshot.docs[0].ref.id} for email ${to}`);
        return { ...tenantSnapshot.docs[0].data(), id: tenantSnapshot.docs[0].ref.id };
    } else {
        logger.info(`Email: No tenant found for email ${to}`);
        return null;
    }
}
