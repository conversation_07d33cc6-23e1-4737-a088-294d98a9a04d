export async function validateFeatures(feature: string, req: any) {
    let leadDocRef: any;
    let leadData: any;
    let leadResult: any;
    const currentTime = Math.floor(Date.now() / 1000);
    let Body = '';
    let messageFormatting = '';
    switch (feature) {
        case 'whatsapp':
            leadResult = await handelLeadDocumentWhatsapp(req);
            leadDocRef = leadResult.leadDocRef;
            leadData = leadResult.leadData;
            break;
        case 'facebook':
            leadResult = await handelLeadDocumentFacebook(req);
            leadDocRef = leadResult.leadDocRef;
            leadData = leadResult.leadData;
            break;
        case 'telegram':
            leadResult = await handelLeadDocumentTelegram(req);
            leadDocRef = leadResult.leadDocRef;
            leadData = leadResult.leadData;
            break;
        case 'email':
            leadResult = await handelLeadDocumentEmail(req);
            leadDocRef = leadResult.leadDocRef;
            leadData = leadResult.leadData;
    }
