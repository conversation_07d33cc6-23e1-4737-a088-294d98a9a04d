import { onRequest } from 'firebase-functions/v2/https';
import * as logger from 'firebase-functions/logger';
import * as admin from 'firebase-admin';
import express from 'express';
// import { twiml } from "twilio";
import {
    invokeTwilio,
    openAIKey,
    twilioAccounSID,
    twilioAuthTpken,
    facebookVerifyToken,
    facebookPageAccessToken,
    telegramBotToken,
    sendGridApiKey,
    assemblyAIApiKey,
    twilioWhatsappNumber,
    getEmailTemplate,
    getDriveClient,
    calendar,
    lifttCalendarId,
    googleServiceAccountEmail,
    googleServiceAccountKey,
    tokenKey,
    invokeOpenAI,
    facebookAppId,
    facebookAppSecret
} from './utils';
import axios from 'axios';
import * as multipart from 'parse-multipart-data';
import sgMail from '@sendgrid/mail';
import { handleUserQuery, sendMessengerMessage, sendTelegramIncomingMessage, sendTelegramMessage } from './chatbots';
import { getCallDetails } from './calls';
import { handelLeadDocumentForm } from './forms';
import { handelLeadDocumentVapiCall } from './chatbots/vapi';
import { processLeadAppointmentFromCalendarEvent } from './appointments';

async function handelLeadDocumentVoice(utterances: any, req: any) {
    const { From, CallerName } = req;

    const leadsCollection = admin.firestore().collection('leads');
    const currentTime = Math.floor(Date.now() / 1000);

    // Search for an existing lead by WaId (WhatsApp ID)
    const leadsSnapshot = await leadsCollection.where('number', '==', From).where('lead_source', '==', 'voice').get();
    let leadDocRef, leadData;

    if (!leadsSnapshot.empty) {
        // Lead exists, get the reference and data
        const leadDoc = leadsSnapshot.docs[0];
        leadDocRef = leadDoc.ref;
        leadData = leadDoc.data();
    } else {
        // Create a new lead document with an autogenerated ID
        leadDocRef = leadsCollection.doc(); // Auto-generate document ID
        leadData = {
            lead_details: req,
            number: From,
            name: CallerName,
            lead_source: 'voice', // Track the source of the lead
            lastMessageTime: currentTime,
            conversations: []
        };
    }

    utterances.forEach((utterance: any) => {
        const speaker = utterance.speaker;
        const message = utterance.text;

        leadData.lead_details.conversations.push({
            role: 'human',
            speaker: speaker,
            message: message,
            lastMessageTime: currentTime
        });
    });
    leadDocRef.set(leadData, { merge: true });
    return { leadDocRef: leadDocRef, leadData: leadData };
}

// Function to send a sender action (e.g., typing_on)
export async function sendMessengerSenderAction(pageId: string, recipientId: string, action: string): Promise<void> {
    try {
        let accessToken = facebookPageAccessToken.value();
        const tenantCollection = admin.firestore().collection('companies');
        const tenantSnapshot = await tenantCollection.where('facebook.pageId', '==', pageId).get();
        if (tenantSnapshot.docs.length > 0) {
            accessToken = tenantSnapshot.docs[0].data()!.facebook?.pageAccessToken;
            logger.info(`Facebook sendMessengerSenderAction: Found access token for tenant ${tenantSnapshot.docs[0].ref.id} for page ${pageId}`);
        } else {
            logger.info(`Facebook sendMessengerSenderAction: No tenant found for page ${pageId}`);
        }

        const data = JSON.stringify({
            recipient: {
                id: recipientId
            },
            sender_action: action
        });

        const config = {
            method: 'post',
            url: `https://graph.facebook.com/me/messages?access_token=${accessToken}`,
            headers: {
                'Content-Type': 'application/json'
            },
            data: data
        };

        const response = await axios(config);
        logger.info('Sender action response:', response.data);
    } catch (error) {
        logger.error('Error sending sender action:', error);
    }
}

const app = express();
app.use(express.json());

app.post('/whatsapp-webhook', async (req, res) => {
    const { WaId } = req.body;
    const twilio = invokeTwilio();
    // const { MessagingResponse } = twilio.twiml;
    // const twimlMsg = new MessagingResponse();
    const fromWhatsapp = `whatsapp:${twilioWhatsappNumber.value()}`;
    const toWhatsapp = `whatsapp:${WaId}`;
    try {
        const response = await handleUserQuery('whatsapp', req.body);
        const messageBody = response.generatedMessage;
        const maxMessageLength = 1600;
        if (messageBody.length > maxMessageLength) {
            const chunks = messageBody.match(new RegExp(`.{1,${maxMessageLength}}`, 'g'));

            if (chunks) {
                for (const chunk of chunks) {
                    await twilio.messages.create({
                        from: fromWhatsapp,
                        body: chunk,
                        to: toWhatsapp
                    });
                }
            }
        } else {
            await twilio.messages.create({
                from: fromWhatsapp,
                body: messageBody,
                to: toWhatsapp
            });
        }

        res.writeHead(200, { 'Content-Type': 'text/xml' });
        res.end('Sent message successfully.');
    } catch (error) {
        logger.error('Error in Twilio webhook:', error);

        await twilio.messages.create({
            from: fromWhatsapp,
            body: 'Sorry, there was an error processing your request.',
            to: toWhatsapp
        });
        res.writeHead(200, { 'Content-Type': 'text/xml' });
        res.end('Sorry, there was an error processing your request.');
    }
});

// Incoming Call Webhook

app.post('/call-recording-webhook', async (req, res) => {
    const { RecordingUrl } = req.body;

    logger.log(`Received Recording URL: ${RecordingUrl}`);

    if (!RecordingUrl) {
        logger.error('No Recording URL provided');
        res.status(400).send('Missing Recording URL');
    } else {
        try {
            // Step 1: Upload the audio file to AssemblyAI
            const uploadResponse = await axios.post(
                'https://api.assemblyai.com/v2/upload',
                { url: `${RecordingUrl}.mp3` },
                {
                    headers: {
                        Authorization: assemblyAIApiKey.value(),
                        'content-type': 'application/json'
                    }
                }
            );

            const audioUrl = uploadResponse.data.upload_url;

            // Step 2: Submit the audio for transcription
            const transcriptionResponse = await axios.post(
                'https://api.assemblyai.com/v2/transcript',
                {
                    audio_url: audioUrl,
                    language_code: 'en_us',
                    speaker_labels: true // Enable speaker diarization
                },
                {
                    headers: {
                        Authorization: assemblyAIApiKey.value(),
                        'content-type': 'application/json'
                    }
                }
            );

            const transcriptId = transcriptionResponse.data.id;
            logger.info(`Transcription ID: ${transcriptId}`);

            // Poll AssemblyAI for transcription completion
            const checkTranscription = async () => {
                try {
                    const result = await axios.get(`https://api.assemblyai.com/v2/transcript/${transcriptId}`, {
                        headers: {
                            Authorization: assemblyAIApiKey.value()
                        }
                    });

                    if (result.data.status === 'completed') {
                        logger.info(`Transcription Completed: ${result.data.text}`);
                        await handelLeadDocumentVoice(result.data, req.body);

                        res.writeHead(200, { 'Content-Type': 'text/xml' });
                        res.end('Sent message successfully.');
                    } else if (result.data.status === 'error') {
                        logger.error('Transcription failed:', result.data.error);

                        throw new Error(`Transcription failed:, ${result.data.error}`);
                    } else {
                        setTimeout(checkTranscription, 5000); // Retry after 5 seconds
                    }
                } catch (error) {
                    res.writeHead(200, { 'Content-Type': 'text/xml' });
                    res.end('Sent message successfully.');
                    logger.error('Error checking transcription status:', error);
                }
            };

            await checkTranscription();

            res.writeHead(200, { 'Content-Type': 'text/xml' });
            res.end('Sent message successfully.');
        } catch (error) {
            logger.error('Error during transcription process:', error);
            res.status(500).send('Error processing transcription');
        }
    }

    res.sendStatus(200);
});

// Facebook Messenger Webhook Verification
app.get('/facebook-webhook', (req, res) => {
    const mode = req.query['hub.mode'];
    const token = req.query['hub.verify_token'];
    const challenge = req.query['hub.challenge'];

    if (mode && token === facebookVerifyToken.value()) {
        res.status(200).send(challenge);
    } else {
        res.sendStatus(403);
    }
});

// Handle incoming messages
app.post('/facebook-webhook', async (req, res) => {
    const body = req.body;
    logger.info('Facebook Webhook Body:', body);
    if (body.object === 'page') {
        for (const entry of body.entry) {
            for (const event of entry.messaging) {
                if (event.message && event.message.text) {
                    if (
                        [
                            "I'm here to assist you with Liftt's premium garage doors and related services. How can I help you today?",
                            "How can I assist you with Liftt's premium garage doors and related services today?",
                            'Liftt offers a variety of garage door sizes to suit different needs:'
                        ].includes(event.message.text)
                    ) {
                        res.writeHead(200, { 'Content-Type': 'text/xml' });
                        res.end('Sent message successfully.');
                        return;
                    }

                    const senderId = event.sender.id;
                    await sendMessengerSenderAction(entry.id, senderId, 'mark_seen');
                    // Show the typing effect
                    await sendMessengerSenderAction(entry.id, senderId, 'typing_on');
                    // Generate response using OpenAI
                    try {
                        const response = await handleUserQuery('facebook', { pageId: entry.id, ...event });
                        const messageBody = response.generatedMessage;
                        // Send response back to Facebook Messenger
                        await sendMessengerMessage(entry.id, senderId, messageBody);
                        await sendMessengerSenderAction(entry.id, senderId, 'typing_off');
                        res.writeHead(200, { 'Content-Type': 'text/xml' });
                        res.end('Sent message successfully.');
                    } catch (error) {
                        logger.error('Error in Facebook webhook:', error);

                        // const twiml = new MessagingResponse();
                        // twiml.message("Sorry, there was an error processing your request.");
                        await sendMessengerMessage(entry.id, senderId, 'Sorry, there was an error processing your request.');
                        await sendMessengerSenderAction(entry.id, senderId, 'typing_off');
                        res.writeHead(200, { 'Content-Type': 'text/xml' });
                        res.end('Sorry, there was an error processing your request.');
                    }
                }
            }
        }
    } else {
        res.writeHead(200, { 'Content-Type': 'text/xml' });
        res.end('Sorry, there was an error processing your request.');
    }
});

// Handle incoming messages
app.post('/telegram-webhook', async (req, res) => {
    const body = req.body;

    const headers = req.headers;

    logger.info('Telegram Webhook Headers:', headers);

    const chatId = body.message.chat.id;
    try {
        await sendTelegramIncomingMessage(chatId);

        const response = await handleUserQuery('telegram', body);
        const messageBody = response.generatedMessage;
        // Send response back to Facebook Messenger
        await sendTelegramMessage(chatId, messageBody);

        res.writeHead(200, { 'Content-Type': 'text/xml' });
        res.end('Sent message successfully.');
    } catch (error) {
        logger.error('Error in Telegram webhook:', error);

        // const twiml = new MessagingResponse();
        // twiml.message("Sorry, there was an error processing your request.");
        await sendTelegramMessage(chatId, 'Sorry, there was an error processing your request.');
        res.writeHead(200, { 'Content-Type': 'text/xml' });
        res.end('Sorry, there was an error processing your request.');
    }
    res.writeHead(200, { 'Content-Type': 'text/xml' });
    res.end('EVENT_RECEIVED');
});

// Endpoint to handle incoming emails
app.post('/email-webhook-listener', async (req, res) => {
    try {
        // Log the entire request body for debugging

        // req is express style request object
        const parts = multipart.parse(req.body, 'xYzZY');
        const parsed: Record<string, any> = {};

        for (const part of parts) {
            const { name, data } = part;

            parsed[name as string] = data.toString();
        }

        if (!req.body || Object.keys(req.body).length === 0) {
            throw new Error('Bad request: Empty body received');
        }

        // Extract relevant email data from the SendGrid Inbound Parse webhook payload
        let { to, from, subject, text, html, attachments } = parsed;

        // Log the extracted email data
        logger.info('Extracted email data:', {
            to,
            from,
            subject,
            text,
            html,
            attachments
        });

        if (subject.includes('Liftt Email Validation Test')) {
        }
        // Check if this is a contact form email from Liftt website
        const isContactForm = text && text.includes('This e-mail was sent from a contact form on Liftt');

        if (isContactForm && from.includes('<EMAIL>')) {
            // Extract the actual sender's email from the body
            const emailRegex = /From: .+<([^>]+)>|([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})/;
            const emailMatch = text.match(emailRegex);

            if (emailMatch) {
                // Replace the from address with the actual sender's email
                const actualEmail = emailMatch[1] || emailMatch[2];
                logger.info('Contact form submission - original from:', from);
                logger.info('Contact form submission - actual sender:', actualEmail);
                from = actualEmail;
                parsed.from = actualEmail;
            }
        }

        // Filter spam/no-reply emails
        const spamPatterns = [
            // No-reply variations
            'noreply',
            'no-reply',
            'donotreply',
            'do-not-reply',
            'no.reply',

            // System emails
            'automated',
            'notification',
            'alert',
            'system',
            'auto-confirm',
            'mailer-daemon',
            'postmaster',
            'bounces',
            'bounce',
            'daemon',

            // Common department emails
            'marketing@',
            'newsletter@',
            'info@',
            'support@',
            'contact@',
            'admin@',
            'help@',
            'service@',
            'updates@',
            'news@',
            'hello@',
            'support@',
            'invoices@',
            'invoice@',
            'billing@',
            'accounts@',
            'customerservice@',
            'feedback@',
            'enquiry@',
            'inquiry@',

            // Promotional
            'promotions@',
            'offers@',
            'deals@',
            'sales@',
            'discount@',
            'campaign@',
            'special@',
            'events@',
            'invitation@',
            'invite@',

            // Notifications
            'notifications@',
            'alerts@',
            'updates@',
            'reminder@',
            'notice@',
            'announcement@',
            'broadcast@',
            'digest@',

            // Security
            'security@',
            'verification@',
            'confirm@',
            'authenticate@',
            'validate@',
            'password@',
            'recovery@',
            'reset@',
            '2fa@',

            // Social media
            'facebook@',
            'twitter@',
            'instagram@',
            'linkedin@',
            'pinterest@',
            'youtube@',
            'social@',
            'connect@',

            // Domains
            '@mailchimp.com',
            '@sendgrid.net',
            '@mailgun.org',
            '@constant.com',
            '@amazonses.com',
            '@email-server.com',
            '@salesforce.com',
            '@hubspot.com',
            '@marketo.com',
            '@campaign-monitor.com',
            '@klaviyo.com',
            '@mailerlite.com',
            '@aweber.com',
            '@getresponse.com',
            '@activecampaign.com',
            '@mail.buzzcentrex.net'
        ];

        const isSpamEmail = spamPatterns.some((pattern) => from.toLowerCase().includes(pattern));

        if (isSpamEmail) {
            logger.info('Irrelevant email detected:', from);
            res.status(200).send('Email received but ignored as irrelevant.');
            return;
        }

        if (to === from || from.includes('<EMAIL>') || from.includes('<EMAIL>')) {
            logger.info('Bad request: Email sent to itself or from sales email');
            res.status(200).send('Email received and processed successfully.');
            return;
        }

        const response = await handleUserQuery('email', parsed);

        // Use SendGrid to send the response email
        const msg = {
            to: from, // Recipient's email address
            from: to, // Dynamically set the sender email
            subject: subject, // Email subject
            html: await getEmailTemplate(response.generatedMessage.replace(/```html\s*|\s*```/g, ''), response.tenantId) // Plain text body
        };
        sgMail.setApiKey(sendGridApiKey.value());
        await sgMail.send(msg);

        // Respond to SendGrid with a 200 OK status
        res.status(200).send('Email received and processed successfully.');
    } catch (error) {
        logger.error('Error processing email:', error);
        // Respond with a 500 error if something goes wrong
        res.status(200).send('Email received and processed successfully.');
    }
});

app.get('/process-call/:callSid?', async (req, res) => {
    const { callSid } = req.params;
    const { date_from, date_to } = req.query;

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!date_from || !date_to) {
        res.status(400).send('Both date_from and date_to are required.');
        return;
    }
    if (!dateRegex.test(date_from as string) || !dateRegex.test(date_to as string)) {
        res.status(400).send('Invalid date format. Use YYYY-MM-DD.');
        return;
    }

    try {
        // Step 1: Fetch call details
        await getCallDetails(callSid ?? null, {
            date_from: date_from,
            date_to: date_to
        });

        res.status(200).send('Call SID processed successfully.');
    } catch (error) {
        logger.error('Error processing call:', error);
        res.status(500).send('Internal Server Error');
    }
    return;
});

app.post('/process-call', async (req, res) => {
    const { callSid } = req.body;
    logger.info('Studio flow called:', req.body);
    logger.info('Call SID to process:', callSid);
    if (!callSid) {
        res.status(400).send('No call SID provided.');
    } else {
        try {
            // Step 1: Fetch call details
            await getCallDetails(callSid);

            res.status(200).send('Call SID processed successfully.');
        } catch (error) {
            logger.error('Error processing call:', error);
            res.status(500).send('Internal Server Error');
        }
    }
});

app.post('/process-form', async (req, res) => {
    try {
        await handelLeadDocumentForm(req.body);
        res.status(200).send('Form processed successfully.');
    } catch (error) {
        logger.error('Error processing form:', error);
        res.status(500).send('Internal Server Error');
    }
});

app.post('/vapi-callback', async (req, res) => {
    try {
        const reqBody = req.body;
        await handelLeadDocumentVapiCall(reqBody);
        res.status(200).send('Call AI Callback processed successfully.');
    } catch (error) {
        logger.error('Error processing Call AI Callback:', error);
        res.status(500).send('Internal Server Error');
    }
});

app.post('/listen-to-appointments', async (req, res) => {
    try {
        const channelToken = req.headers['x-goog-channel-token'];
        if (channelToken !== tokenKey.replace(/\n/g, '')) {
            logger.error('Invalid token');
            return res.status(401).send('Invalid token');
        }

        const auth = await getDriveClient();

        // Initialize variables for pagination
        let nextPageToken = null;
        let allEvents: any[] = [];
        let batch = admin.firestore().batch();

        // Set up time range for events
        const timeMin = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString();
        const timeMax = new Date(new Date().setMonth(new Date().getMonth() + 1)).toISOString();
        const datesProcessed: any[] = [];
        // Fetch events with pagination
        do {
            const eventResponse: any = await calendar.events.list({
                auth: auth,
                calendarId: lifttCalendarId,
                timeMax: timeMax,
                timeMin: timeMin,
                maxResults: 50, // Smaller page size for better performance
                singleEvents: true,
                showDeleted: true,
                orderBy: 'updated',
                pageToken: nextPageToken || undefined
            });

            // Store the next page token
            nextPageToken = eventResponse.data.nextPageToken || null;

            // Process current page of events
            if (eventResponse.data?.items && eventResponse.data.items.length > 0) {
                const currentPageCount = eventResponse.data.items.length;
                allEvents = [...allEvents, ...eventResponse.data.items];

                logger.info(`Processing page with ${currentPageCount} calendar events (total so far: ${allEvents.length})`);

                // Process events in current page

                for (const item of eventResponse.data.items) {
                    try {
                        await processLeadAppointmentFromCalendarEvent(batch, item);
                        // store all events that has appointments
                        if (item.summary && !item.summary.includes('No Appointments') && item.status !== 'cancelled') {
                            datesProcessed.push(item.start.dateTime);
                        }

                        logger.info(`Completed processing event: ${item.id}`);
                    } catch (error) {
                        logger.error(`Error processing calendar event ${item.id}:`, error);
                    }
                }
            } else {
                logger.info('No calendar events found in current page');
            }

            await batch.commit();
            // Create a new batch for the next set of operations
            batch = admin.firestore().batch();
            // Continue fetching pages until there are no more
        } while (nextPageToken);

        // get all the dates that does not have appointments using timeMin and timeMax
        const datesWithoutAppointments = getDatesWithoutAppointments(timeMin, timeMax, datesProcessed);

        if (datesWithoutAppointments?.length > 0) {
            try {
                // use openai to get the available slots for the datesWithoutAppointments make the array of objects with human readable time slots to be used in calls
                const openai = invokeOpenAI();
                const response = await openai.chat.completions.create({
                    model: 'gpt-4o',
                    messages: [
                        {
                            role: 'system',
                            content:
                                "You are given a list of available appointment dates. Remove all weekend dates (Saturday and Sunday). Detect full weeks (all 5 weekdays). If multiple full weeks are consecutive, group them as 'Available the entire week of [Month] [Day] until [Month] [Day]'. For partial weeks, use 'Monday, June 17 until Wednesday, June 19'. Combine all ranges into one line separated by commas. End with '(9am to 12pm)' once. Do not repeat the time for each range. Respond only with the formatted plain text output."
                        },
                        {
                            role: 'user',
                            content: `Available dates:\n${JSON.stringify(datesWithoutAppointments)}\n\nExample output:\nAvailable the entire week of June 17 until June 28, Monday, July 1 until Thursday, July 11 (9am to 12pm)`
                        }
                    ]
                });
                const result = response.choices[0].message.content as string;

                // update the datesWithoutAppointments in the ai_params collection
                const aiFollowUpDoc = admin.firestore().collection('settings').doc('ai_params');
                batch.set(
                    aiFollowUpDoc,
                    {
                        followup: { datesWithoutAppointments: result }
                    },
                    { merge: true }
                );

                const aiFollowUpConversationDoc = admin.firestore().collection('settings').doc('conversations');
                batch.set(
                    aiFollowUpConversationDoc,
                    {
                        call: { availableAppointmentDates: datesWithoutAppointments }
                    },
                    { merge: true }
                );
            } catch (error) {
                logger.error(`Error processing dates without appointments:`, error);
            }
        }
        // Commit any remaining operations in the batch
        await batch.commit();

        logger.info(`All calendar events processed successfully `);

        return res.status(200).send('Success');
    } catch (error) {
        logger.error('Error processing event:', error instanceof Error ? error.message : 'Unknown error');
        return res.status(500).send('Internal Server Error');
    }
});

const getDatesWithoutAppointments = (timeMin: string, timeMax: string, datesProcessed: string[]) => {
    const startDate = new Date(timeMin);
    const endDate = new Date(timeMax);
    const datesWithoutAppointments = [];
    const currentDate = new Date(startDate);

    // Convert appointment dates to YYYY-MM-DD format for comparison
    const appointmentDates = datesProcessed.map((dateString) => {
        // Parse the ISO date with timezone offset
        const date = new Date(dateString);
        // Format to YYYY-MM-DD
        return date.toISOString().split('T')[0];
    });

    logger.info(`Normalized appointment dates: ${JSON.stringify(appointmentDates)}`);

    while (currentDate <= endDate) {
        // Skip weekends (0 = Sunday, 6 = Saturday)
        const dayOfWeek = currentDate.getDay();
        if (dayOfWeek !== 0 && dayOfWeek !== 6) {
            const dateString = currentDate.toISOString().split('T')[0];

            // Check if this date has any appointments
            const hasAppointment = appointmentDates.includes(dateString);

            if (!hasAppointment) {
                datesWithoutAppointments.push(dateString);
            }
        }

        // Move to next day
        currentDate.setDate(currentDate.getDate() + 1);
    }

    logger.info(`Found ${datesWithoutAppointments.length} weekdays without appointments`);
    return datesWithoutAppointments;
};

app.get('/auth-facebook-callback', async (req, res) => {
    try {
        logger.info('Req Params is', req.params);
        logger.info('Req Query is', req.query);

        // Exchange code for access token
        const tokenResponse = await axios.get('https://graph.facebook.com/oauth/access_token', {
            params: {
                client_id: facebookAppId.value(),
                client_secret: facebookAppSecret.value(),
                grant_type: 'client_credentials'
            }
        });

        const { access_token } = tokenResponse.data;

        // Get user email
        const userResponse = await axios.get('https://graph.facebook.com/me', {
            params: {
                fields: 'email',
                access_token
            }
        });

        logger.info('User info is', userResponse.data);
        // const email = userResponse.data.email;

        // // Send confirmation message to user
        // await axios.post(
        //   `https://graph.facebook.com/me/messages?access_token=${facebookPageAccessToken.value()}`,
        //   {
        //     recipient: { id: recipientId },
        //     message: { text: `Thanks! Your email is: ${email}` },
        //   },
        // );

        res.send('Authentication successful!');
    } catch (error) {
        logger.error('OAuth error:', error);
        res.status(500).send('Authentication failed');
    }
});

// Export the chatbots webhook handler
export const chatBots = onRequest(
    {
        cors: true,
        secrets: [openAIKey, twilioAuthTpken, twilioAccounSID, twilioWhatsappNumber, facebookVerifyToken, facebookPageAccessToken, telegramBotToken, sendGridApiKey, assemblyAIApiKey, googleServiceAccountEmail, googleServiceAccountKey, facebookAppId],
        cpu: 8,
        memory: '16GiB',
        timeoutSeconds: 3600
    },
    app
);
